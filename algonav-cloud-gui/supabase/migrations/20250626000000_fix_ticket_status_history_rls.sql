-- Migration: Fix ticket_status_history RLS policy
-- Adds INSERT policy for ticket_status_history table to allow trigger-based inserts

-- Add INSERT policy for ticket_status_history
-- This allows inserts when the user has permission to update the related ticket
CREATE POLICY "ticket_status_history_insert" ON "public"."ticket_status_history"
    AS permissive FOR INSERT TO public
    WITH CHECK (EXISTS (
        SELECT 1 FROM public.tickets
        WHERE tickets.id = ticket_status_history.ticket_id
        AND (tickets.creator_id = auth.uid() OR tickets.assignee_id = auth.uid())
    ));
