# Ticketing System Documentation

## Overview

The AlgoNav Cloud GUI includes a comprehensive ticketing system that allows users to create, manage, and track support requests. The system is fully integrated with the existing job, task, and dataset management features, providing contextual support directly from the job detail pages.

## Features

- **Contextual Ticket Creation**: Create support tickets directly from job and task contexts
- **Smart Ticket Display**: Intelligent ticket visibility based on job-only vs. task-specific tickets
- **Visual Status Indicators**: Color-coded support icons and status pills showing ticket states
- **Dual Interface**: Drawer interface on ticket pages, modal interface on job detail pages
- **Real-time Updates**: Immediate cache invalidation and live updates
- **Role-based Controls**: Different interfaces for support staff vs. customers
- **Target Linking**: Link tickets to specific jobs, tasks, or datasets
- **Status Management**: Track ticket progress through various states
- **Messages System**: Add messages and collaborate on tickets
- **Dashboard Integration**: View ticket summaries on the main dashboard

## Database Schema

### Tables

#### `tickets`
- `id` (integer, primary key)
- `creator_id` (uuid, foreign key to auth.users)
- `assignee_id` (uuid, foreign key to auth.users, nullable)
- `title` (text, required)
- `description` (text, nullable)
- `status` (ticket_status enum, default: 'open')
- `priority` (ticket_priority enum, default: 'medium')
- `created_at` (timestamptz)
- `updated_at` (timestamptz)
- `updated_by` (uuid, foreign key to auth.users)

#### `ticket_targets`
- `id` (integer, primary key)
- `ticket_id` (integer, foreign key to tickets)
- `target_type` (text, check: 'job', 'task', 'dataset')
- `target_id` (integer)

#### `ticket_messages`
- `id` (integer, primary key)
- `ticket_id` (integer, foreign key to tickets)
- `author_id` (uuid, foreign key to auth.users)
- `body` (text, required)
- `created_at` (timestamptz)

#### `ticket_status_history`
- `id` (integer, primary key)
- `ticket_id` (integer, foreign key to tickets)
- `old_status` (ticket_status enum, nullable)
- `new_status` (ticket_status enum)
- `changed_by` (uuid, foreign key to auth.users)
- `changed_at` (timestamptz)

### Enums

#### `ticket_status`
- `open`: Newly created tickets (Orange/Warning color)
- `waiting_on_customer`: Waiting for customer response (Blue/Info color) - replaces `in_progress`
- `resolved`: Issue has been resolved (Green/Success color)
- `closed`: Ticket is closed (Gray/Default color)
- `cancelled`: Ticket was cancelled (Red/Error color)

#### `ticket_priority`
- `low`: Low priority issues
- `medium`: Standard priority (default)
- `high`: High priority issues
- `urgent`: Critical issues requiring immediate attention

## API Endpoints

### Core Ticket Operations

#### `GET /api/tickets`
List tickets with optional filtering
- Query parameters: `status`, `myTickets`, `page`, `limit`
- Returns: Array of tickets with creator, assignee, and targets

#### `POST /api/tickets`
Create a new ticket
- Body: `{ title, description?, priority?, assigneeId?, targets? }`
- Returns: Created ticket data

#### `GET /api/tickets/[id]`
Get a specific ticket with full details
- Returns: Ticket with comments and status history

#### `PUT /api/tickets/[id]`
Update ticket details
- Body: `{ title?, description?, priority?, assigneeId? }`
- Returns: Updated ticket data

#### `PATCH /api/tickets/[id]/status`
Update ticket status
- Body: `{ status }`
- Returns: Updated ticket data
- Automatically creates status history entry

#### `GET /api/tickets/by-target`
Get tickets by target (job, task, or dataset)
- Query parameters: `target_type`, `target_id`, `job_only`
- `job_only=true`: Returns only tickets linked exclusively to jobs (no task targets)
- Returns: Array of tickets for the specified target

### Message Operations

#### `GET /api/tickets/[id]/messages`
Get messages for a ticket
- Query parameters: `page`, `limit`
- Returns: Array of messages with author information

#### `POST /api/tickets/[id]/messages`
Add a message to a ticket
- Body: `{ body }`
- Returns: Created message data

## React Components

### Core Components

#### `TicketList`
Displays a paginated, sortable table of tickets
- Props: `tickets`, `loading`, `onTicketClick`, `onStatusFilter`, `onSearch`
- Features: Search, status filtering, sorting, pagination

#### `TicketDetailDrawer`
Side drawer showing full ticket details (used on ticket pages)
- Props: `open`, `ticket`, `onClose`, `onStatusChange`, `onAddMessage`
- Features: Status updates, message thread, ticket metadata, timestamps footer

#### `TicketDetailModal`
Modal dialog showing full ticket details (used on job detail pages)
- Props: `open`, `ticket`, `onClose`, `onStatusChange`, `onAddMessage`
- Features: Same as drawer but in modal format

#### `TicketDetailContent`
Reusable content component for ticket details
- Used by both TicketDetailDrawer and TicketDetailModal
- Features: Role-based UI (support staff vs. customer), contextual actions

#### `SupportIconWithStatus`
Smart support icon with ticket status indication
- Props: `targetType`, `targetId`, `onTicketClick`, `onCreateTicket`, `variant`, `jobOnly`
- Features: Color-coded icons, status pills, click handling for view/create

#### `MyTicketsWidget`
Dashboard widget showing ticket summary
- Props: `tickets`, `loading`, `error`, `onTicketClick`
- Features: Status counts, recent tickets list

### Integration Components

#### Updated `SupportDialog`
Enhanced to create tickets directly
- Automatically links tickets to current job/task/dataset context
- Uses `useCreateTicket` hook for immediate ticket creation
- Shows confirmation message after successful creation

#### Updated Job Components
- **JobHeader**: Shows support icon for job-only tickets (using `jobOnly=true`)
- **TaskRow**: Shows support icon for task-specific tickets
- **Job Detail Page**: Integrates TicketDetailModal for ticket viewing

## React Query Hooks

### Data Fetching Hooks

#### `useTickets(filters)`
Fetch tickets with filtering and real-time updates
- Parameters: `{ status?, myTickets?, page?, limit? }`
- Returns: Query result with tickets array

#### `useTicket(id)`
Fetch single ticket with real-time subscriptions
- Parameters: Ticket ID
- Returns: Query result with full ticket details
- Features: Automatic updates via Supabase realtime

#### `useTicketsByTarget(targetType, targetId, jobOnly?)`
Fetch tickets for a specific target (job, task, or dataset)
- Parameters: Target type, target ID, job-only flag
- Returns: Query result with first matching ticket or null
- Features: Real-time updates, 5-second refresh interval

#### `useTicketStatus(targetType, targetId, jobOnly?)`
Helper hook to get ticket status for UI display
- Returns: `{ hasTicket, status, ticketId, isLoading }`

### Mutation Hooks

#### `useCreateTicket()`
Create new tickets with optimistic updates
- Returns: Mutation function and state

#### `useUpdateTicket()`
Update ticket details with optimistic updates
- Returns: Mutation function and state

#### `useUpdateTicketStatus()`
Update ticket status with optimistic updates
- Returns: Mutation function and state

#### `useAddMessage()`
Add messages with optimistic updates
- Returns: Mutation function and state
- Features: Automatic cache invalidation for immediate UI updates

## Smart Ticket Display Logic

### Job-Only vs. Task-Specific Tickets

The system intelligently displays tickets based on their target associations:

#### Job Header Display
- Shows tickets that are linked **only** to the job (no task targets)
- Uses `jobOnly=true` parameter in API calls
- Example: General job configuration issues, job-wide problems

#### Task Row Display
- Shows tickets that are linked to the specific task
- Includes tickets that have both job and task targets
- Uses `jobOnly=false` parameter in API calls
- Example: Task-specific processing errors, task data issues

### Visual Status Indicators

#### Support Icon Colors
- **Gray**: No ticket exists
- **Orange**: Open ticket
- **Blue**: Waiting on customer
- **Green**: Resolved ticket
- **Gray**: Closed ticket

#### Status Pills
- Displayed next to support icons when tickets exist
- Show current ticket status in human-readable format
- Color-coded to match the icon colors

## Role-Based User Interface

### Support Staff Interface
- **Full Status Control**: Dropdown to change ticket status to any state
- **All Status Options**: Open, Waiting on Customer, Resolved, Closed, Cancelled
- **Administrative View**: Can see assignee, creator, and full ticket metadata

### Customer Interface
- **Status Display**: Shows current status as a colored pill
- **Contextual Actions**: Only relevant actions based on current status
  - **Close Ticket**: Available for Open, Waiting on Customer, Resolved tickets
  - **Reopen Ticket**: Available for Closed tickets
- **No Free Status Selection**: Cannot arbitrarily change status

## Real-time Features

The ticketing system uses multiple mechanisms to provide immediate updates:

### Real-time Subscriptions
- **Ticket Updates**: Changes to ticket status, priority, or details
- **New Messages**: Real-time message notifications
- **Status History**: Live status change tracking
- Subscriptions are automatically managed by hooks and clean up when components unmount

### Immediate Cache Updates
- **Cache Invalidation**: Automatic invalidation of relevant queries after ticket operations
- **Force Refetch**: Immediate refetch of active queries for instant UI updates
- **5-Second Intervals**: Regular polling for the latest data
- **Optimistic Updates**: UI updates immediately while API calls are in progress

## Access Control

### Row Level Security (RLS)

#### Tickets
- Users can access tickets where they are creator or assignee
- Admins have full access to all tickets

#### Comments
- Users can read/write comments on tickets they have access to
- Comment authors are automatically set to current user

#### Targets
- Access controlled through parent ticket permissions
- Read-only after creation

#### Status History
- Read-only for all users with ticket access
- Automatically populated by database triggers

## Usage Examples

### Creating a Ticket from Job Context

```typescript
const createTicket = useCreateTicket();

const handleCreateTicket = async () => {
  await createTicket.mutateAsync({
    title: "Job processing issue",
    description: "Job failed with error XYZ",
    priority: "high",
    targets: [{
      target_type: "job",
      target_id: jobId,
      name: jobName
    }]
  });
};
```

### Updating Ticket Status

```typescript
const updateStatus = useUpdateTicketStatus();

const handleStatusChange = async (ticketId: string, newStatus: string) => {
  await updateStatus.mutateAsync({
    id: ticketId,
    status: newStatus
  });
};
```

### Adding Messages

```typescript
const addMessage = useAddMessage();

const handleAddMessage = async (ticketId: string, message: string) => {
  await addMessage.mutateAsync({
    ticketId,
    body: message
  });
};
```

### Using Smart Support Icons

```typescript
// In JobHeader - shows only job-specific tickets
<SupportIconWithStatus
  targetType="job"
  targetId={jobId}
  onTicketClick={handleOpenTicketModal}
  onCreateTicket={handleOpenSupportDialog}
  variant="button"
  jobOnly={true}
/>

// In TaskRow - shows task-specific tickets
<SupportIconWithStatus
  targetType="task"
  targetId={taskId}
  onTicketClick={handleOpenTicketModal}
  onCreateTicket={handleOpenSupportDialog}
  variant="icon"
  jobOnly={false}
/>
```

## Implementation Details

### Cache Management
The system uses React Query for efficient data management:

```typescript
// Immediate cache invalidation after ticket creation
queryClient.invalidateQueries({
  queryKey: ['tickets-by-target'],
  exact: false
});

// Force refetch of active queries
queryClient.refetchQueries({
  queryKey: ['tickets-by-target'],
  exact: false,
  type: 'active'
});
```

### Job-Only Filtering Logic
```typescript
// API logic for job-only tickets
if (jobOnly && targetType === 'job') {
  // Get all tickets and filter client-side
  const filteredTickets = allTickets?.filter(ticket => {
    const hasRequestedJobTarget = ticket.ticket_targets?.some(target =>
      target.target_type === 'job' && target.target_id === targetIdNum
    );
    const hasAnyTaskTargets = ticket.ticket_targets?.some(target =>
      target.target_type === 'task'
    );
    return hasRequestedJobTarget && !hasAnyTaskTargets;
  });
}
```

### Component Architecture
```
Job Detail Page
├── JobHeader
│   └── SupportIconWithStatus (jobOnly=true)
├── TasksTable
│   └── TaskRow
│       └── SupportIconWithStatus (jobOnly=false)
└── TicketDetailModal
    └── TicketDetailContent

Ticket Page
└── TicketDetailDrawer
    └── TicketDetailContent
```

## Migration and Deployment

1. **Database Migration**: Run the migration file to create tables and triggers
2. **API Deployment**: Deploy new API routes including `/api/tickets/by-target`
3. **Frontend Deployment**: Deploy updated components and hooks
4. **Cache Configuration**: Ensure React Query is properly configured
5. **Testing**: Verify all functionality works correctly

## Future Enhancements

- **Email notifications** for ticket updates and status changes
- **File attachments** to tickets and messages
- **Ticket templates** for common issues and faster creation
- **Advanced search and filtering** across all ticket fields
- **Ticket analytics and reporting** for support metrics
- **Integration with external support systems** (Zendesk, Jira, etc.)
- **Bulk operations** for managing multiple tickets
- **SLA tracking** and automated escalation
- **Knowledge base integration** for self-service support
