import { createTheme } from "@mui/material";

const theme = (fontFamily) => createTheme({
    palette: {
      primary: {
        main: '#302c59',
        light: '#ebeaf3',  // <PERSON><PERSON> heller für bessere Lesbarkeit
        dark: '#1e1b37',   // <PERSON><PERSON><PERSON> für Hover-Effekte
      },
      secondary: {
        main: '#FC6200',  // Correct orange color for AlgoNav
        light: '#FFEBD9',  // Light orange for backgrounds
        dark: '#D95400',   // Darker orange for hover effects
      },
      action: {
        active: '#302c59',
        hover: "rgba(48, 44, 89, 0.08)",  // Sehr dezenter Hover-Effekt
        selected: "rgba(48, 44, 89, 0.12)",  // Leicht stärker für Selektion
        disabled: 'rgba(0, 0, 0, 0.26)',
      },
      grey: {
        100: '#F7F7F7',
        200: '#f4f4f4',  // Für subtile Hintergründe
        300: '#e0e0e0',  // Für Borders
        500: '#868686',
      },
      background: {
        default: '#FFFFFF',
        paper: '#FFFFFF',
      },
      common: {
        white: '#FFFFFF',
      },
      // Ticket status colors
      ticket: {
        open: '#1976d2',
        in_progress: '#0288d1',
        waiting_on_customer: '#ed6c02',
        resolved: '#2e7d32',
        closed: '#757575',
        cancelled: '#d32f2f'
      },
    },
    typography: {
      fontFamily: fontFamily,
    },
    components: {
      MuiLink: {
        styleOverrides: {
          root: {
            color: '#302c59',
          },
        },
      },
      MuiListItem: {
        styleOverrides: {
          root: {
            '&.Mui-selected': {
              backgroundColor: 'rgba(252, 98, 0, 0.12)',
              '&:hover': {
                backgroundColor: 'rgba(252, 98, 0, 0.16)',
              },
              // Special styling for template selection will be applied directly in the component
            },
          },
        },
      },
    },
  });

  export default theme