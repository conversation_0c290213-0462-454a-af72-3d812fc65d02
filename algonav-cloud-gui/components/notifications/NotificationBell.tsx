"use client"

import React from 'react';
import {
  Icon<PERSON>utton,
  Badge,
  Tooltip,
  Popover,
  Box,
  CircularProgress
} from '@mui/material';
import NotificationsIcon from '@mui/icons-material/Notifications';
import { useNotifications } from '@/lib/hooks/useNotifications';
import NotificationDropdown from './NotificationDropdown';

interface NotificationBellProps {
  color?: 'inherit' | 'primary' | 'secondary' | 'error' | 'info' | 'success' | 'warning';
}

const NotificationBell: React.FC<NotificationBellProps> = ({ 
  color = 'inherit' 
}) => {
  const {
    notifications,
    unreadCount,
    isLoading,
    error,
    isDropdownOpen,
    handleDropdownToggle,
    handleDropdownClose
  } = useNotifications();

  const [anchorEl, setAnchorEl] = React.useState<HTMLButtonElement | null>(null);

  const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget);
    handleDropdownToggle();
  };

  const handleClose = () => {
    setAnchorEl(null);
    handleDropdownClose();
  };

  // Show loading indicator if still loading
  if (isLoading) {
    return (
      <Tooltip title="Loading notifications...">
        <IconButton color={color} disabled>
          <CircularProgress size={20} color="inherit" />
        </IconButton>
      </Tooltip>
    );
  }

  // Show error state if there's an error
  if (error) {
    console.error('Notification error:', error);
    return (
      <Tooltip title={`Error loading notifications: ${error.message || 'Unknown error'}`}>
        <IconButton color={color} disabled>
          <NotificationsIcon sx={{ opacity: 0.5 }} />
        </IconButton>
      </Tooltip>
    );
  }

  return (
    <>
      <Tooltip title="Notifications">
        <IconButton
          color={color}
          onClick={handleClick}
          aria-controls={isDropdownOpen ? 'notification-menu' : undefined}
          aria-haspopup="true"
          aria-expanded={isDropdownOpen ? 'true' : undefined}
        >
          <Badge 
            badgeContent={unreadCount} 
            color="secondary"
            invisible={unreadCount === 0}
            max={99}
          >
            <NotificationsIcon />
          </Badge>
        </IconButton>
      </Tooltip>

      <Popover
        id="notification-menu"
        open={isDropdownOpen}
        anchorEl={anchorEl}
        onClose={handleClose}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'right',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'right',
        }}
        PaperProps={{
          sx: {
            mt: 1,
            minWidth: 360,
            maxWidth: 400,
            maxHeight: 500,
            overflow: 'hidden',
            boxShadow: (theme) => theme.shadows[8],
          }
        }}
      >
        <Box>
          <NotificationDropdown
            notifications={notifications}
            onNotificationClick={handleClose}
          />
        </Box>
      </Popover>
    </>
  );
};

export default NotificationBell;
