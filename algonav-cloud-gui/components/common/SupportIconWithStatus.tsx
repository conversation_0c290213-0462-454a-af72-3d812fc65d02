import React from 'react';
import {
  <PERSON>con<PERSON><PERSON>on,
  <PERSON><PERSON><PERSON>,
  <PERSON>ack,
  Chip,
  Button,
  CircularProgress
} from '@mui/material';
import { useTheme } from '@mui/material/styles';
import SupportIcon from './SupportIcon';
import {
  useTicketsByTarget,
  getTicketStatusColor,
  getTicketStatusText
} from '../../lib/hooks/useTicketsByTarget';

interface SupportIconWithStatusProps {
  targetType: 'job' | 'task' | 'dataset';
  targetId: number;
  onTicketClick: (ticket: any) => void;
  onCreateTicket: () => void;
  variant?: 'icon' | 'button'; // icon for TaskRow, button for JobHeader
  size?: 'small' | 'medium';
  className?: string;
  jobOnly?: boolean; // For job headers to only show job-specific tickets
}

const SupportIconWithStatus: React.FC<SupportIconWithStatusProps> = ({
  targetType,
  targetId,
  onTicketClick,
  onCreateTicket,
  variant = 'icon',
  size = 'small',
  className = '',
  jobOnly = false
}) => {
  const theme = useTheme();
  const { data: ticket, isLoading } = useTicketsByTarget(targetType, targetId, jobOnly);
  const hasTicket = !!ticket;
  const status = ticket?.status || null;

  const handleClick = () => {
    if (hasTicket && ticket) {
      // Pass the full ticket object to the click handler
      onTicketClick(ticket);
    } else {
      onCreateTicket();
    }
  };

  const getIconColor = () => {
    if (isLoading) return 'text.secondary';
    if (!hasTicket) return 'text.secondary';

    switch (status) {
      case 'open':
        return 'warning.main';
      case 'waiting_on_customer':
        return 'info.main';
      case 'resolved':
        return 'success.main';
      case 'closed':
        return 'text.secondary';
      default:
        return 'text.secondary';
    }
  };

  const getSvgIconColor = () => {
    if (isLoading) return theme.palette.text.primary;
    if (!hasTicket) return theme.palette.text.primary; // Default color

    switch (status) {
      case 'open':
        return theme.palette.warning.main;
      case 'waiting_on_customer':
        return theme.palette.info.main;
      case 'resolved':
        return theme.palette.success.main;
      case 'closed':
        return theme.palette.text.primary;
      default:
        return theme.palette.text.primary;
    }
  };

  const getTooltipText = () => {
    if (isLoading) return 'Loading ticket status...';
    if (!hasTicket) return `Request support for this ${targetType}`;
    return `View ticket (${getTicketStatusText(status)})`;
  };

  if (variant === 'button') {
    // Button variant for JobHeader
    return (
      <Stack direction="row" spacing={1} alignItems="center">
        <Button
          variant="outlined"
          color={hasTicket && status && getTicketStatusColor(status) !== 'default' ? getTicketStatusColor(status) as any : 'primary'}
          size={size}
          startIcon={
            isLoading ? (
              <CircularProgress size={16} />
            ) : (
              <SupportIcon width={16} height={16} color={getSvgIconColor()} />
            )
          }
          onClick={handleClick}
          disabled={isLoading}
          sx={{
            color: getIconColor(),
            borderColor: getIconColor(),
            '&:hover': {
              borderColor: getIconColor(),
              backgroundColor: 'rgba(0, 0, 0, 0.04)'
            }
          }}
        >
          {hasTicket ? 'View Ticket' : 'Request Support'}
        </Button>
        
        {hasTicket && status && (
          <Chip
            label={getTicketStatusText(status)}
            color={getTicketStatusColor(status) as any}
            size="small"
            variant="outlined"
          />
        )}
      </Stack>
    );
  }

  // Icon variant for TaskRow
  return (
    <Stack direction="row" spacing={1} alignItems="center">
      <Tooltip title={getTooltipText()}>
        <span>
          <IconButton
            size={size}
            onClick={handleClick}
            disabled={isLoading}
            aria-label={getTooltipText()}
            sx={{
              color: getIconColor(),
              '&:hover': {
                color: hasTicket ? getIconColor() : 'primary.main'
              }
            }}
            className={className}
          >
            {isLoading ? (
              <CircularProgress size={20} />
            ) : (
              <SupportIcon width={20} height={20} color={getSvgIconColor()} />
            )}
          </IconButton>
        </span>
      </Tooltip>
      
      {hasTicket && status && (
        <Chip
          label={getTicketStatusText(status)}
          color={getTicketStatusColor(status) as any}
          size="small"
          variant="outlined"
          sx={{ 
            fontSize: '0.75rem',
            height: '20px',
            '& .MuiChip-label': {
              px: 1
            }
          }}
        />
      )}
    </Stack>
  );
};

export default SupportIconWithStatus;
