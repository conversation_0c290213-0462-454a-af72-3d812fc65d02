'use client';

import React from 'react';
import {
  Drawer,
  Box,
  Typography,
  IconButton,
  Stack
} from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import TicketDetailContent from './TicketDetailContent';

interface TicketDetailDrawerProps {
  open: boolean;
  ticket: any;
  onClose: () => void;
  onStatusChange?: (ticketId: string, status: string) => Promise<void> | void;
  onAddMessage?: (ticketId: string, message: string) => Promise<void> | void;
  loading?: boolean;
}

export function TicketDetailDrawer({
  open,
  ticket,
  onClose,
  onStatusChange,
  onAddMessage,
  loading = false
}: TicketDetailDrawerProps) {
  return (
    <Drawer
      anchor="right"
      open={open}
      onClose={onClose}
      PaperProps={{
        sx: {
          width: { xs: '100%', sm: 600 },
          maxWidth: '100vw'
        }
      }}
    >
      <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
        {/* Header */}
        <Box sx={{ p: 2, borderBottom: '1px solid', borderColor: 'divider' }}>
          <Stack direction="row" alignItems="center" justifyContent="space-between">
            <Typography variant="h6" component="h2">
              {ticket ? `Ticket #${ticket.id}` : 'Ticket Details'}
            </Typography>
            <IconButton onClick={onClose}>
              <CloseIcon />
            </IconButton>
          </Stack>
        </Box>

        {/* Content */}
        <Box sx={{ flex: 1, overflow: 'auto', p: 2 }}>
          <TicketDetailContent
            ticket={ticket}
            loading={loading}
            onStatusChange={onStatusChange}
            onAddMessage={onAddMessage}
          />
        </Box>

        {/* Footer with timestamps */}
        {ticket && (
          <Box sx={{ p: 2, borderTop: '1px solid', borderColor: 'divider' }}>
            <Stack direction="row" justifyContent="space-between">
              <Typography variant="caption" color="text.secondary">
                Created: {new Date(ticket.created_at).toLocaleString()}
              </Typography>
              <Typography variant="caption" color="text.secondary">
                Updated: {new Date(ticket.updated_at).toLocaleString()}
              </Typography>
            </Stack>
          </Box>
        )}
      </Box>
    </Drawer>
  );
}


