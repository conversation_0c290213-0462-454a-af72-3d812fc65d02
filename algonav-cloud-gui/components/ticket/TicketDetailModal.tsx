import React from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  IconButton,
  Box,
  Typography
} from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import TicketDetailContent from './TicketDetailContent';

interface TicketDetailModalProps {
  open: boolean;
  ticket: any;
  onClose: () => void;
  onStatusChange?: (ticketId: string, newStatus: string) => void;
  onAddMessage?: (ticketId: string, message: string) => void;
  loading?: boolean;
}

const TicketDetailModal: React.FC<TicketDetailModalProps> = ({
  open,
  ticket,
  onClose,
  onStatusChange,
  onAddMessage,
  loading = false
}) => {
  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="md"
      fullWidth
      PaperProps={{
        sx: {
          maxHeight: '90vh',
          height: 'auto'
        }
      }}
    >
      {/* Header */}
      <DialogTitle>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <Typography variant="h6" component="h2">
            {ticket ? `Ticket #${ticket.id}` : 'Ticket Details'}
          </Typography>
          <IconButton
            onClick={onClose}
            size="small"
            sx={{
              color: 'text.secondary',
              '&:hover': {
                color: 'text.primary'
              }
            }}
          >
            <CloseIcon />
          </IconButton>
        </Box>
      </DialogTitle>

      {/* Content */}
      <DialogContent dividers sx={{ p: 3 }}>
        <TicketDetailContent
          ticket={ticket}
          loading={loading}
          onStatusChange={onStatusChange}
          onAddMessage={onAddMessage}
        />
      </DialogContent>
    </Dialog>
  );
};

export default TicketDetailModal;
