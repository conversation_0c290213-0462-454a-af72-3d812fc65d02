import React from 'react';
import {
  <PERSON><PERSON>,
  Typography,
  IconButton,
  Toolt<PERSON>,
  Chip
} from '@mui/material';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import SupportIconWithStatus from '../common/SupportIconWithStatus';
import { Job } from '../../types/job'; // Changed Batch to Job
import { formatJobName, getStatusDisplay } from '../../utils/jobUtils'; // Changed formatBatchName to formatJobName

interface JobHeaderProps {
  job: Job; // Changed batch to job
  router: any;
  onOpenJobSupport: () => void;
  onOpenTicketModal: (ticket: any) => void; // New prop for opening ticket modal
}

const JobHeader: React.FC<JobHeaderProps> = ({ job, router, onOpenJobSupport, onOpenTicketModal }) => { // Changed batch to job
  const status = getStatusDisplay(job.status); // Changed batch.status to job.status

  return (
    <Stack 
      direction="row" 
      alignItems="center" 
      spacing={2}
      sx={{ 
        backgroundColor: 'background.paper',
        p: 2,
        borderBottom: '1px solid',
        borderColor: 'divider'
      }}
    >
      <IconButton 
        onClick={() => router.push('/jobs')} 
        size="small"
        sx={{ color: 'text.secondary' }}
        aria-label="Back to jobs list"
      >
        <ArrowBackIcon />
      </IconButton>
      <Typography variant="h6" component="h1" sx={{ flexGrow: 1, fontWeight: 500 }}>
        {formatJobName(job.name)} {/* Display the actual job name */}
      </Typography>
      <SupportIconWithStatus
        targetType="job"
        targetId={parseInt(job.id)}
        onTicketClick={onOpenTicketModal}
        onCreateTicket={onOpenJobSupport}
        variant="button"
        size="small"
        jobOnly={true}
      />
      <Tooltip title={status.tooltip || ''} arrow>
        <Chip
          label={status.text}
          color={status.color as 'default' | 'primary' | 'secondary' | 'error' | 'info' | 'success' | 'warning' | undefined}
          size="small"
          sx={{ fontWeight: 500 }}
        />
      </Tooltip>
    </Stack>
  );
};

export default JobHeader;
