import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { TicketList } from '@/components/ticket/TicketList';

// Mock date-fns
jest.mock('date-fns', () => ({
  formatDistanceToNow: jest.fn(() => '2 hours ago')
}));

const mockTickets = [
  {
    id: '1',
    title: 'Test Ticket 1',
    description: 'Test description 1',
    status: 'open' as const,
    priority: 'high' as const,
    created_at: '2023-01-01T00:00:00Z',
    updated_at: '2023-01-01T00:00:00Z',
    creator: { id: 'user1', email: '<EMAIL>' },
    assignee: { id: 'user2', email: '<EMAIL>' },
    ticket_targets: [{ target_type: 'job', target_id: 123 }]
  },
  {
    id: '2',
    title: 'Test Ticket 2',
    description: 'Test description 2',
    status: 'in_progress' as const,
    priority: 'medium' as const,
    created_at: '2023-01-02T00:00:00Z',
    updated_at: '2023-01-02T00:00:00Z',
    creator: { id: 'user1', email: '<EMAIL>' },
    ticket_targets: []
  }
];

describe('TicketList', () => {
  const mockOnTicketClick = jest.fn();
  const mockOnStatusFilter = jest.fn();
  const mockOnSearch = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders tickets correctly', () => {
    render(
      <TicketList
        tickets={mockTickets}
        onTicketClick={mockOnTicketClick}
        onStatusFilter={mockOnStatusFilter}
        onSearch={mockOnSearch}
      />
    );

    expect(screen.getByText('Test Ticket 1')).toBeInTheDocument();
    expect(screen.getByText('Test Ticket 2')).toBeInTheDocument();
    expect(screen.getByText('Open')).toBeInTheDocument();
    expect(screen.getByText('In Progress')).toBeInTheDocument();
  });

  it('calls onTicketClick when ticket row is clicked', () => {
    render(
      <TicketList
        tickets={mockTickets}
        onTicketClick={mockOnTicketClick}
        onStatusFilter={mockOnStatusFilter}
        onSearch={mockOnSearch}
      />
    );

    const ticketRow = screen.getByText('Test Ticket 1').closest('tr');
    fireEvent.click(ticketRow!);

    expect(mockOnTicketClick).toHaveBeenCalledWith(mockTickets[0]);
  });

  it('filters tickets by status', async () => {
    render(
      <TicketList
        tickets={mockTickets}
        onTicketClick={mockOnTicketClick}
        onStatusFilter={mockOnStatusFilter}
        onSearch={mockOnSearch}
      />
    );

    const statusSelect = screen.getByLabelText('Status');
    fireEvent.mouseDown(statusSelect);
    
    const openOption = screen.getByText('Open');
    fireEvent.click(openOption);

    await waitFor(() => {
      expect(mockOnStatusFilter).toHaveBeenCalledWith('open');
    });
  });

  it('searches tickets', async () => {
    render(
      <TicketList
        tickets={mockTickets}
        onTicketClick={mockOnTicketClick}
        onStatusFilter={mockOnStatusFilter}
        onSearch={mockOnSearch}
      />
    );

    const searchInput = screen.getByPlaceholderText('Search tickets...');
    fireEvent.change(searchInput, { target: { value: 'test search' } });

    await waitFor(() => {
      expect(mockOnSearch).toHaveBeenCalledWith('test search');
    });
  });

  it('displays assignee information correctly', () => {
    render(
      <TicketList
        tickets={mockTickets}
        onTicketClick={mockOnTicketClick}
        onStatusFilter={mockOnStatusFilter}
        onSearch={mockOnSearch}
      />
    );

    expect(screen.getByText('user2')).toBeInTheDocument();
    expect(screen.getByText('Unassigned')).toBeInTheDocument();
  });

  it('handles pagination correctly', () => {
    const manyTickets = Array.from({ length: 25 }, (_, i) => ({
      ...mockTickets[0],
      id: `${i + 1}`,
      title: `Ticket ${i + 1}`
    }));

    render(
      <TicketList
        tickets={manyTickets}
        onTicketClick={mockOnTicketClick}
        onStatusFilter={mockOnStatusFilter}
        onSearch={mockOnSearch}
      />
    );

    // Should show pagination controls
    expect(screen.getByText('1–10 of 25')).toBeInTheDocument();
    
    // Should show next page button
    const nextButton = screen.getByLabelText('Go to next page');
    expect(nextButton).toBeInTheDocument();
  });

  it('sorts tickets correctly', () => {
    render(
      <TicketList
        tickets={mockTickets}
        onTicketClick={mockOnTicketClick}
        onStatusFilter={mockOnStatusFilter}
        onSearch={mockOnSearch}
      />
    );

    const titleHeader = screen.getByText('Title');
    fireEvent.click(titleHeader);

    // Should trigger sorting (implementation depends on parent component)
    expect(titleHeader).toBeInTheDocument();
  });

  it('displays loading state', () => {
    render(
      <TicketList
        tickets={[]}
        loading={true}
        onTicketClick={mockOnTicketClick}
        onStatusFilter={mockOnStatusFilter}
        onSearch={mockOnSearch}
      />
    );

    // The loading state is handled by the parent component
    // This test ensures the component doesn't crash with empty tickets
    expect(screen.getByText('Search tickets...')).toBeInTheDocument();
  });

  it('displays priority chips correctly', () => {
    render(
      <TicketList
        tickets={mockTickets}
        onTicketClick={mockOnTicketClick}
        onStatusFilter={mockOnStatusFilter}
        onSearch={mockOnSearch}
      />
    );

    expect(screen.getByText('High')).toBeInTheDocument();
    expect(screen.getByText('Medium')).toBeInTheDocument();
  });

  it('truncates long descriptions', () => {
    const ticketWithLongDescription = {
      ...mockTickets[0],
      description: 'This is a very long description that should be truncated when displayed in the table to prevent layout issues and maintain readability'
    };

    render(
      <TicketList
        tickets={[ticketWithLongDescription]}
        onTicketClick={mockOnTicketClick}
        onStatusFilter={mockOnStatusFilter}
        onSearch={mockOnSearch}
      />
    );

    const descriptionElement = screen.getByText(ticketWithLongDescription.description);
    expect(descriptionElement).toHaveStyle({
      overflow: 'hidden',
      textOverflow: 'ellipsis',
      whiteSpace: 'nowrap'
    });
  });
});
