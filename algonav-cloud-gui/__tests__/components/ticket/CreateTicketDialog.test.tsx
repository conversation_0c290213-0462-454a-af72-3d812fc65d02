import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { CreateTicketDialog } from '@/components/ticket/CreateTicketDialog';

describe('CreateTicketDialog', () => {
  const mockOnClose = jest.fn();
  const mockOnSubmit = jest.fn();

  const defaultProps = {
    open: true,
    onClose: mockOnClose,
    onSubmit: mockOnSubmit,
    loading: false
  };

  beforeEach(() => {
    jest.clearAllMocks();
    mockOnSubmit.mockResolvedValue(undefined);
  });

  it('renders dialog correctly when open', () => {
    render(<CreateTicketDialog {...defaultProps} />);

    expect(screen.getByText('Create Support Ticket')).toBeInTheDocument();
    expect(screen.getByLabelText('Title')).toBeInTheDocument();
    expect(screen.getByLabelText('Description')).toBeInTheDocument();
    expect(screen.getByLabelText('Priority')).toBeInTheDocument();
    expect(screen.getByText('Create Ticket')).toBeInTheDocument();
  });

  it('does not render when closed', () => {
    render(<CreateTicketDialog {...defaultProps} open={false} />);

    expect(screen.queryByText('Create Support Ticket')).not.toBeInTheDocument();
  });

  it('validates required fields', async () => {
    render(<CreateTicketDialog {...defaultProps} />);

    const submitButton = screen.getByText('Create Ticket');
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText('Title is required')).toBeInTheDocument();
    });

    expect(mockOnSubmit).not.toHaveBeenCalled();
  });

  it('submits form with valid data', async () => {
    render(<CreateTicketDialog {...defaultProps} />);

    const titleInput = screen.getByLabelText('Title');
    const descriptionInput = screen.getByLabelText('Description');
    const submitButton = screen.getByText('Create Ticket');

    fireEvent.change(titleInput, { target: { value: 'Test Ticket' } });
    fireEvent.change(descriptionInput, { target: { value: 'Test Description' } });
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(mockOnSubmit).toHaveBeenCalledWith({
        title: 'Test Ticket',
        description: 'Test Description',
        priority: 'medium',
        assigneeId: undefined,
        targets: undefined
      });
    });
  });

  it('handles priority selection', async () => {
    render(<CreateTicketDialog {...defaultProps} />);

    const titleInput = screen.getByLabelText('Title');
    const prioritySelect = screen.getByLabelText('Priority');

    fireEvent.change(titleInput, { target: { value: 'Test Ticket' } });
    fireEvent.mouseDown(prioritySelect);
    
    const highOption = screen.getByText('High');
    fireEvent.click(highOption);

    const submitButton = screen.getByText('Create Ticket');
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(mockOnSubmit).toHaveBeenCalledWith({
        title: 'Test Ticket',
        description: undefined,
        priority: 'high',
        assigneeId: undefined,
        targets: undefined
      });
    });
  });

  it('displays preselected targets', () => {
    const preselectedTargets = [
      { target_type: 'job' as const, target_id: 123, name: 'Test Job' },
      { target_type: 'task' as const, target_id: 456, name: 'Test Task' }
    ];

    render(
      <CreateTicketDialog 
        {...defaultProps} 
        preselectedTargets={preselectedTargets}
      />
    );

    expect(screen.getByText('Job "Test Job"')).toBeInTheDocument();
    expect(screen.getByText('Task "Test Task"')).toBeInTheDocument();
  });

  it('allows removing targets', async () => {
    const preselectedTargets = [
      { target_type: 'job' as const, target_id: 123, name: 'Test Job' }
    ];

    render(
      <CreateTicketDialog 
        {...defaultProps} 
        preselectedTargets={preselectedTargets}
      />
    );

    const targetChip = screen.getByText('Job "Test Job"');
    expect(targetChip).toBeInTheDocument();

    // Find and click the delete button on the chip
    const deleteButton = targetChip.parentElement?.querySelector('[data-testid="CancelIcon"]');
    if (deleteButton) {
      fireEvent.click(deleteButton);
    }

    await waitFor(() => {
      expect(screen.queryByText('Job "Test Job"')).not.toBeInTheDocument();
    });
  });

  it('handles assignee input', async () => {
    render(<CreateTicketDialog {...defaultProps} />);

    const titleInput = screen.getByLabelText('Title');
    const assigneeInput = screen.getByLabelText('Assignee Email (Optional)');

    fireEvent.change(titleInput, { target: { value: 'Test Ticket' } });
    fireEvent.change(assigneeInput, { target: { value: '<EMAIL>' } });

    const submitButton = screen.getByText('Create Ticket');
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(mockOnSubmit).toHaveBeenCalledWith({
        title: 'Test Ticket',
        description: undefined,
        priority: 'medium',
        assigneeId: '<EMAIL>',
        targets: undefined
      });
    });
  });

  it('closes dialog on cancel', () => {
    render(<CreateTicketDialog {...defaultProps} />);

    const cancelButton = screen.getByText('Cancel');
    fireEvent.click(cancelButton);

    expect(mockOnClose).toHaveBeenCalled();
  });

  it('closes dialog on close icon click', () => {
    render(<CreateTicketDialog {...defaultProps} />);

    const closeButton = screen.getByLabelText('Close');
    fireEvent.click(closeButton);

    expect(mockOnClose).toHaveBeenCalled();
  });

  it('disables form when loading', () => {
    render(<CreateTicketDialog {...defaultProps} loading={true} />);

    expect(screen.getByLabelText('Title')).toBeDisabled();
    expect(screen.getByLabelText('Description')).toBeDisabled();
    expect(screen.getByLabelText('Priority')).toBeDisabled();
    expect(screen.getByText('Cancel')).toBeDisabled();
  });

  it('shows loading state when submitting', async () => {
    let resolveSubmit: (value: any) => void;
    const submitPromise = new Promise((resolve) => {
      resolveSubmit = resolve;
    });
    mockOnSubmit.mockReturnValue(submitPromise);

    render(<CreateTicketDialog {...defaultProps} />);

    const titleInput = screen.getByLabelText('Title');
    fireEvent.change(titleInput, { target: { value: 'Test Ticket' } });

    const submitButton = screen.getByText('Create Ticket');
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText('Creating...')).toBeInTheDocument();
    });

    // Resolve the promise to complete the test
    resolveSubmit!(undefined);
  });

  it('resets form when dialog opens', () => {
    const { rerender } = render(<CreateTicketDialog {...defaultProps} open={false} />);

    // Open dialog and fill form
    rerender(<CreateTicketDialog {...defaultProps} open={true} />);
    
    const titleInput = screen.getByLabelText('Title');
    fireEvent.change(titleInput, { target: { value: 'Test Title' } });

    // Close and reopen dialog
    rerender(<CreateTicketDialog {...defaultProps} open={false} />);
    rerender(<CreateTicketDialog {...defaultProps} open={true} />);

    // Form should be reset
    expect(screen.getByLabelText('Title')).toHaveValue('');
  });

  it('handles submission errors gracefully', async () => {
    const consoleErrorSpy = jest.spyOn(console, 'error').mockImplementation();
    mockOnSubmit.mockRejectedValue(new Error('Submission failed'));

    render(<CreateTicketDialog {...defaultProps} />);

    const titleInput = screen.getByLabelText('Title');
    fireEvent.change(titleInput, { target: { value: 'Test Ticket' } });

    const submitButton = screen.getByText('Create Ticket');
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText('Failed to create ticket. Please try again.')).toBeInTheDocument();
    });

    expect(consoleErrorSpy).toHaveBeenCalledWith('Failed to create ticket:', expect.any(Error));
    consoleErrorSpy.mockRestore();
  });
});
