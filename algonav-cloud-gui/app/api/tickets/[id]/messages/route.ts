import { withAuth } from '@/lib/api/withAuth';
import { NextResponse } from 'next/server';
import { createClient } from "@/utils/supabase/server";

export const POST = withAuth(async (userId, request, { params }) => {
    const supabase = createClient();
    const { id } = params;
    const { body } = await request.json();

    if (!id) {
        return NextResponse.json({ error: 'Ticket ID is required' }, { status: 400 });
    }

    // Validate message body
    if (!body || typeof body !== 'string' || body.trim() === '') {
        return NextResponse.json({ error: 'Message body is required' }, { status: 400 });
    }

    try {
        // First verify the ticket exists and user has access
        const { data: ticket, error: ticketError } = await supabase
            .from('tickets')
            .select('id')
            .eq('id', id)
            .single();

        if (ticketError) {
            if (ticketError.code === 'PGRST116') {
                return NextResponse.json({ error: 'Ticket not found or access denied' }, { status: 404 });
            }
            return NextResponse.json({ error: ticketError.message }, { status: 500 });
        }

        // Create the message
        const { data: message, error } = await supabase
            .from('ticket_messages')
            .insert({
                ticket_id: parseInt(id as string),
                author_id: userId,
                body: body.trim()
            })
            .select('*')
            .single();

        if (error) {
            return NextResponse.json({ error: error.message }, { status: 500 });
        }

        // Update the ticket's updated_at timestamp
        await supabase
            .from('tickets')
            .update({
                updated_at: new Date().toISOString(),
                updated_by: userId
            })
            .eq('id', id);

        // Fetch user information separately
        const { data: users, error: usersError } = await supabase.auth.admin.listUsers();

        if (!usersError && users) {
            const userMap = new Map();
            users.users.forEach(user => {
                userMap.set(user.id, { id: user.id, email: user.email });
            });

            // Add user information to message
            message.author = userMap.get(message.author_id) || null;
        }

        return NextResponse.json({ success: true, data: message });
    } catch (error) {
        console.error('Message creation error:', error);
        return NextResponse.json({ error: 'Failed to create message' }, { status: 500 });
    }
});

export const GET = withAuth(async (userId, request, { params }) => {
    const supabase = createClient();
    const { id } = params;
    const { searchParams } = new URL(request.url);
    
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '50');
    const offset = (page - 1) * limit;

    if (!id) {
        return NextResponse.json({ error: 'Ticket ID is required' }, { status: 400 });
    }

    try {
        // First verify the ticket exists and user has access
        const { data: ticket, error: ticketError } = await supabase
            .from('tickets')
            .select('id')
            .eq('id', id)
            .single();

        if (ticketError) {
            if (ticketError.code === 'PGRST116') {
                return NextResponse.json({ error: 'Ticket not found or access denied' }, { status: 404 });
            }
            return NextResponse.json({ error: ticketError.message }, { status: 500 });
        }

        // Fetch messages for the ticket
        const { data: messages, error } = await supabase
            .from('ticket_messages')
            .select('*')
            .eq('ticket_id', id)
            .order('created_at', { ascending: true })
            .range(offset, offset + limit - 1);

        if (error) {
            return NextResponse.json({ error: error.message }, { status: 500 });
        }

        // Fetch user information separately for message authors
        if (messages && messages.length > 0) {
            const userIds = new Set<string>();
            messages.forEach(message => {
                if (message.author_id) userIds.add(message.author_id);
            });

            const { data: users, error: usersError } = await supabase.auth.admin.listUsers();

            if (!usersError && users) {
                const userMap = new Map();
                users.users.forEach(user => {
                    userMap.set(user.id, { id: user.id, email: user.email });
                });

                // Add user information to messages
                messages.forEach(message => {
                    message.author = userMap.get(message.author_id) || null;
                });
            }
        }

        return NextResponse.json({ success: true, data: messages });
    } catch (error) {
        console.error('Messages fetch error:', error);
        return NextResponse.json({ error: 'Failed to fetch messages' }, { status: 500 });
    }
});
