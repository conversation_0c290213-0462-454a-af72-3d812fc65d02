import { withAuth } from '@/lib/api/withAuth';
import { NextResponse } from 'next/server';
import { createClient } from "@/utils/supabase/server";

export const PATCH = withAuth(async (userId, request) => {
    const supabase = createClient();

    try {
        // Update the user's last_seen_at timestamp to now
        const { data, error } = await supabase
            .from('user_notification_states')
            .upsert({
                user_id: userId,
                last_seen_at: new Date().toISOString()
            }, {
                onConflict: 'user_id'
            })
            .select()
            .single();

        if (error) {
            console.error('Error updating notification state:', error);
            return NextResponse.json({ error: 'Failed to mark notifications as seen' }, { status: 500 });
        }

        return NextResponse.json({
            success: true,
            data: {
                lastSeenAt: data.last_seen_at
            }
        });

    } catch (error) {
        console.error('Error in mark-seen API:', error);
        return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
    }
});
