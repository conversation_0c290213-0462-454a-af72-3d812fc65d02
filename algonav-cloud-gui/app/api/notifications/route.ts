import { withAuth } from '@/lib/api/withAuth';
import { NextResponse } from 'next/server';
import { createClient } from "@/utils/supabase/server";

export const GET = withAuth(async (userId, request) => {
    const supabase = createClient();
    const { searchParams } = new URL(request.url);
    const limit = parseInt(searchParams.get('limit') || '50');

    try {
        // Get or create user notification state
        const { data: notificationState, error: stateError } = await supabase
            .rpc('get_or_create_notification_state', { p_user_id: userId });

        if (stateError) {
            console.error('Error getting notification state:', stateError);
            return NextResponse.json({ error: 'Failed to get notification state' }, { status: 500 });
        }

        const lastSeenAt = notificationState?.[0]?.last_seen_at || new Date(0).toISOString();

        // Get tickets that have been updated to resolved or waiting_on_customer
        // since the user's last seen timestamp
        const { data: notifications, error: notificationsError } = await supabase
            .from('tickets')
            .select(`
                id,
                title,
                status,
                updated_at,
                creator_id,
                assignee_id,
                ticket_targets(
                    target_type,
                    target_id
                )
            `)
            .eq('creator_id', userId)
            .in('status', ['resolved', 'waiting_on_customer'])
            .gte('updated_at', lastSeenAt)
            .order('updated_at', { ascending: false })
            .limit(limit);

        if (notificationsError) {
            console.error('Error fetching notifications:', notificationsError);
            return NextResponse.json({ error: 'Failed to fetch notifications' }, { status: 500 });
        }

        // Count unread notifications
        const unreadCount = notifications?.length || 0;

        return NextResponse.json({
            success: true,
            data: {
                notifications: notifications || [],
                unreadCount,
                lastSeenAt
            }
        });

    } catch (error) {
        console.error('Error in notifications API:', error);
        return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
    }
});
