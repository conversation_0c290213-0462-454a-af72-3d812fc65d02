import { useQuery, useQueryClient } from '@tanstack/react-query'
import { useEffect } from 'react'
import { createClient } from '@/utils/supabase/client'
import { api } from '../services/api'

export function useTicketsByTarget(
  targetType: 'job' | 'task' | 'dataset' | null,
  targetId: number | null,
  jobOnly: boolean = false
) {
  const queryClient = useQueryClient()
  const supabase = createClient()

  const query = useQuery({
    queryKey: ['tickets-by-target', targetType, targetId, jobOnly],
    queryFn: () => api.getTicketsByTarget(targetType!, targetId!, jobOnly),
    enabled: !!(targetType && targetId),
    refetchInterval: 5000, // Refresh every 5 seconds for faster updates
    retry: 2,
    select: (data) => {
      // Return the first ticket if any exist, or null
      return data && data.length > 0 ? data[0] : null;
    }
  })

  // Set up realtime subscription for ticket changes related to this target
  useEffect(() => {
    if (!targetType || !targetId) return

    const channel = supabase
      .channel(`tickets-target-${targetType}-${targetId}-${jobOnly}`)
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'tickets'
        },
        (payload) => {
          console.log('Ticket updated:', payload)
          // Invalidate queries for this target
          queryClient.invalidateQueries({
            queryKey: ['tickets-by-target', targetType, targetId, jobOnly]
          })
          // Also invalidate general tickets queries
          queryClient.invalidateQueries({ queryKey: ['tickets'] })
        }
      )
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'ticket_targets'
        },
        (payload) => {
          console.log('Ticket target updated:', payload)
          // Check if this target change affects our query
          const newRecord = payload.new as any
          const oldRecord = payload.old as any
          
          if (
            (newRecord?.target_type === targetType && newRecord?.target_id === targetId) ||
            (oldRecord?.target_type === targetType && oldRecord?.target_id === targetId)
          ) {
            queryClient.invalidateQueries({
              queryKey: ['tickets-by-target', targetType, targetId, jobOnly]
            })
          }
        }
      )
      .subscribe()

    return () => {
      supabase.removeChannel(channel)
    }
  }, [targetType, targetId, jobOnly, queryClient, supabase])

  return query
}

// Helper hook to get ticket status for display
export function useTicketStatus(
  targetType: 'job' | 'task' | 'dataset' | null,
  targetId: number | null,
  jobOnly: boolean = false
) {
  const { data: ticket, isLoading } = useTicketsByTarget(targetType, targetId, jobOnly)
  
  return {
    hasTicket: !!ticket,
    status: ticket?.status || null,
    ticketId: ticket?.id || null,
    isLoading
  }
}

// Helper function to get status color for UI
export function getTicketStatusColor(status: string | null): string {
  switch (status) {
    case 'open':
      return 'warning' // orange/yellow
    case 'waiting_on_customer':
      return 'info' // blue
    case 'resolved':
      return 'success' // green
    case 'closed':
      return 'default' // gray
    default:
      return 'default' // gray for no ticket
  }
}

// Helper function to get status display text
export function getTicketStatusText(status: string | null): string {
  switch (status) {
    case 'open':
      return 'Open'
    case 'waiting_on_customer':
      return 'Waiting on Customer'
    case 'resolved':
      return 'Resolved'
    case 'closed':
      return 'Closed'
    default:
      return 'No Ticket'
  }
}
