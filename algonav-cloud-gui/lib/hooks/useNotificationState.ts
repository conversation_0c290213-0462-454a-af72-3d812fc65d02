"use client"

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useAuthStore } from '@/lib/stores/authStore';
import { api } from '@/lib/services/api';

export interface NotificationState {
  user_id: string;
  last_seen_at: string;
  created_at: string;
  updated_at: string;
}

export function useNotificationState() {
  const { user } = useAuthStore();
  const queryClient = useQueryClient();

  // Get current notification state
  const {
    data: notificationState,
    isLoading,
    error
  } = useQuery<NotificationState>({
    queryKey: ['notification-state'],
    queryFn: async () => {
      // This will be handled by the main notifications query
      // We can extract the lastSeenAt from there
      const notificationData = await api.getNotifications(1);
      return {
        user_id: user?.id || '',
        last_seen_at: notificationData.lastSeenAt,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };
    },
    enabled: !!user,
    staleTime: 60000, // Consider data stale after 1 minute
  });

  // Update last seen timestamp
  const updateLastSeenMutation = useMutation({
    mutationFn: api.markNotificationsAsSeen,
    onSuccess: (data) => {
      // Update both notification state and notifications cache
      queryClient.setQueryData(['notification-state'], (oldData: NotificationState | undefined) => {
        if (!oldData) return oldData;
        return {
          ...oldData,
          last_seen_at: data.lastSeenAt,
          updated_at: new Date().toISOString()
        };
      });
      
      // Also invalidate notifications to refresh unread count
      queryClient.invalidateQueries({ queryKey: ['notifications'] });
    },
    onError: (error) => {
      console.error('Failed to update notification state:', error);
    }
  });

  // Helper function to check if a timestamp is after last seen
  const isAfterLastSeen = (timestamp: string): boolean => {
    if (!notificationState?.last_seen_at) return true;
    return new Date(timestamp) > new Date(notificationState.last_seen_at);
  };

  // Helper function to get unread count for a list of notifications
  const getUnreadCount = (notifications: Array<{ updated_at: string }>): number => {
    if (!notificationState?.last_seen_at) return notifications.length;
    return notifications.filter(notification => 
      isAfterLastSeen(notification.updated_at)
    ).length;
  };

  return {
    notificationState,
    isLoading,
    error,
    updateLastSeen: updateLastSeenMutation.mutate,
    isUpdating: updateLastSeenMutation.isPending,
    isAfterLastSeen,
    getUnreadCount
  };
}
